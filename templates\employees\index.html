{% extends "base.html" %}

{% block title %}إدارة الموظفين - نظام الشؤون القانونية{% endblock %}

{% block content %}
<!-- رأس التقرير للطباعة -->
{% include 'components/print_header.html' with context %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة الموظفين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ {% url 'employees.add' %} }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة موظف جديد
            </a>
            <a href="{{ {% url 'employees.salary_report' %} }}" class="btn btn-warning">
                <i class="fas fa-money-bill-wave me-1"></i>
                تقرير الرواتب
            </a>
            <button onclick="exportEmployees()" class="btn btn-outline-success">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total }}</h4>
                        <p class="card-text">إجمالي الموظفين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.active }}</h4>
                        <p class="card-text">الموظفون النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.full_time }}</h4>
                        <p class="card-text">دوام كامل</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.departments_count }}</h4>
                        <p class="card-text">الأقسام</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ {% url 'employees.index' %} }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="الاسم، رقم الموظف، البريد...">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="department" class="form-label">القسم</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {{ 'selected' if department_id == dept.id|string }}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" {{ 'selected' if employment_status == 'active' }}>نشط</option>
                            <option value="inactive" {{ 'selected' if employment_status == 'inactive' }}>غير نشط</option>
                            <option value="terminated" {{ 'selected' if employment_status == 'terminated' }}>منتهي الخدمة</option>
                            <option value="resigned" {{ 'selected' if employment_status == 'resigned' }}>مستقيل</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="type" class="form-label">نوع التوظيف</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">جميع الأنواع</option>
                            <option value="full_time" {{ 'selected' if employment_type == 'full_time' }}>دوام كامل</option>
                            <option value="part_time" {{ 'selected' if employment_type == 'part_time' }}>دوام جزئي</option>
                            <option value="contract" {{ 'selected' if employment_type == 'contract' }}>عقد</option>
                            <option value="intern" {{ 'selected' if employment_type == 'intern' }}>متدرب</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="sort" class="form-label">ترتيب حسب</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="created_at" {{ 'selected' if sort_by == 'created_at' }}>تاريخ الإضافة</option>
                            <option value="name" {{ 'selected' if sort_by == 'name' }}>الاسم</option>
                            <option value="employee_number" {{ 'selected' if sort_by == 'employee_number' }}>رقم الموظف</option>
                            <option value="hire_date" {{ 'selected' if sort_by == 'hire_date' }}>تاريخ التوظيف</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الموظفين ({{ employees.total }} موظف)
        </h5>
    </div>
    <div class="card-body">
        {% if employees.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الموظف</th>
                        <th>الاسم</th>
                        <th>القسم</th>
                        <th>المنصب</th>
                        <th>نوع التوظيف</th>
                        <th>الراتب</th>
                        <th>الحالة</th>
                        <th>تاريخ التوظيف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>
                            <strong>{{ employee.employee_number }}</strong>
                        </td>
                        <td>
                            <a href="{{ url_for('employees.view', id=employee.id) }}" class="text-decoration-none">
                                {{ employee.full_name }}
                            </a>
                            {% if employee.email %}
                            <br><small class="text-muted">{{ employee.email }}</small>
                            {% endif %}
                        </td>
                        <td>{{ employee.department.name if employee.department else 'غير محدد' }}</td>
                        <td>{{ employee.position or 'غير محدد' }}</td>
                        <td>
                            {% if employee.employment_type == 'full_time' %}
                                <span class="badge bg-primary">دوام كامل</span>
                            {% elif employee.employment_type == 'part_time' %}
                                <span class="badge bg-info">دوام جزئي</span>
                            {% elif employee.employment_type == 'contract' %}
                                <span class="badge bg-warning">عقد</span>
                            {% elif employee.employment_type == 'intern' %}
                                <span class="badge bg-secondary">متدرب</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ employee.employment_type or 'غير محدد' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.total_salary %}
                            <strong>{{ "{:,.0f}"|employee.total_salary }}</strong>
                            <small class="text-muted">ريال</small>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.employment_status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                            {% elif employee.employment_status == 'inactive' %}
                                <span class="badge bg-warning">غير نشط</span>
                            {% elif employee.employment_status == 'terminated' %}
                                <span class="badge bg-danger">منتهي الخدمة</span>
                            {% elif employee.employment_status == 'resigned' %}
                                <span class="badge bg-secondary">مستقيل</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ employee.employment_status or 'غير محدد' }}</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.hire_date|date:"%Y-%m-%d" if employee.hire_date else 'غير محدد' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('employees.view', id=employee.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employees.edit', id=employee.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if employee.employment_status == 'active' %}
                                <button onclick="terminateEmployee('{{ employee.id }}', '{{ employee.full_name }}')"
                                        class="btn btn-outline-danger" title="إنهاء الخدمة">
                                    <i class="fas fa-user-times"></i>
                                </button>
                                {% else %}
                                <button onclick="activateEmployee('{{ employee.id }}', '{{ employee.full_name }}')"
                                        class="btn btn-outline-success" title="تفعيل">
                                    <i class="fas fa-user-check"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if employees.pages > 1 %}
        <nav aria-label="صفحات الموظفين">
            <ul class="pagination justify-content-center">
                {% if employees.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employees.index', page=employees.prev_num, search=search, department=department_id, status=employment_status, type=employment_type, sort=sort_by, order=sort_order) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in employees.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != employees.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('employees.index', page=page_num, search=search, department=department_id, status=employment_status, type=employment_type, sort=sort_by, order=sort_order) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if employees.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employees.index', page=employees.next_num, search=search, department=department_id, status=employment_status, type=employment_type, sort=sort_by, order=sort_order) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد موظفين</h5>
            <p class="text-muted">لم يتم العثور على أي موظفين تطابق معايير البحث</p>
            <a href="{{ {% url 'employees.add' %} }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة موظف جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
function terminateEmployee(employeeId, employeeName) {
    if (confirm(`هل أنت متأكد من إنهاء خدمة الموظف "${employeeName}"؟`)) {
        fetch(`/employees/${employeeId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function activateEmployee(employeeId, employeeName) {
    if (confirm(`هل أنت متأكد من تفعيل الموظف "${employeeName}"؟`)) {
        fetch(`/employees/${employeeId}/activate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function exportEmployees() {
    window.location.href = '/employees/export';
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === =   'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<!-- التوقيعات للطباعة -->
{% include 'components/print_signatures.html' with context %}

{% endblock %}
