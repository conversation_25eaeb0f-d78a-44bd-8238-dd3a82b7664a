{% extends "base.html" %}

{% block title %}تعديل العقد {{ contract.contract_number }} - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        تعديل العقد: {{ contract.contract_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('contracts.view', id=contract.id) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i>
                عرض العقد
            </a>
        </div>
        <a href="{{ {% url 'contracts.index' %} }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<form method="POST" enctype="multipart/form-data" id="contractForm">
    <div class="row">
        <!-- العمود الأيسر -->
        <div class="col-md-8">
            <!-- معلومات العقد الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات العقد الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">عنوان العقد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="{{ contract.title }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title_en" class="form-label">عنوان العقد (بالإنجليزية)</label>
                                <input type="text" class="form-control" id="title_en" name="title_en" value="{{ contract.title_en or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contract_type" class="form-label">نوع العقد <span class="text-danger">*</span></label>
                                <select class="form-select" id="contract_type" name="contract_type" required>
                                    <option value="">اختر نوع العقد</option>
                                    <option value="استشارة قانونية" {{ 'selected' if contract.contract_type == 'استشارة قانونية' }}>استشارة قانونية</option>
                                    <option value="توكيل قضائي" {{ 'selected' if contract.contract_type == 'توكيل قضائي' }}>توكيل قضائي</option>
                                    <option value="صياغة عقود" {{ 'selected' if contract.contract_type == 'صياغة عقود' }}>صياغة عقود</option>
                                    <option value="تمثيل قانوني" {{ 'selected' if contract.contract_type == 'تمثيل قانوني' }}>تمثيل قانوني</option>
                                    <option value="خدمات قانونية شاملة" {{ 'selected' if contract.contract_type == 'خدمات قانونية شاملة' }}>خدمات قانونية شاملة</option>
                                    <option value="أخرى" {{ 'selected' if contract.contract_type == 'أخرى' }}>أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contract_type_en" class="form-label">نوع العقد (بالإنجليزية)</label>
                                <input type="text" class="form-control" id="contract_type_en" name="contract_type_en" value="{{ contract.contract_type_en or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assigned_lawyer_id" class="form-label">المحامي المسؤول</label>
                                <select class="form-select" id="assigned_lawyer_id" name="assigned_lawyer_id">
                                    <option value="">اختر المحامي</option>
                                    {% for lawyer in lawyers %}
                                    <option value="{{ lawyer.id }}" {{ 'selected' if contract.assigned_lawyer_id == lawyer.id }}>
                                        {{ lawyer.user.full_name }} - {{ lawyer.specialization }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العميل</label>
                                <input type="text" class="form-control" value="{{ contract.client_name }}" readonly>
                                <div class="form-text">لا يمكن تغيير العميل بعد إنشاء العقد</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف العقد</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ contract.description or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description_en" class="form-label">وصف العقد (بالإنجليزية)</label>
                        <textarea class="form-control" id="description_en" name="description_en" rows="3">{{ contract.description_en or '' }}</textarea>
                    </div>
                </div>
            </div>
            
            <!-- بنود العقد -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-ul me-2"></i>
                        بنود العقد والشروط
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="terms_and_conditions" class="form-label">البنود والشروط</label>
                        <textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" rows="6">{{ contract.terms_and_conditions or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="terms_and_conditions_en" class="form-label">البنود والشروط (بالإنجليزية)</label>
                        <textarea class="form-control" id="terms_and_conditions_en" name="terms_and_conditions_en" rows="6">{{ contract.terms_and_conditions_en or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_terms" class="form-label">شروط الدفع</label>
                        <textarea class="form-control" id="payment_terms" name="payment_terms" rows="3">{{ contract.payment_terms or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_terms_en" class="form-label">شروط الدفع (بالإنجليزية)</label>
                        <textarea class="form-control" id="payment_terms_en" name="payment_terms_en" rows="3">{{ contract.payment_terms_en or '' }}</textarea>
                    </div>
                </div>
            </div>
            
            <!-- ملاحظات إضافية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        ملاحظات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ contract.notes or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes_en" class="form-label">ملاحظات (بالإنجليزية)</label>
                        <textarea class="form-control" id="notes_en" name="notes_en" rows="3">{{ contract.notes_en or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="tags" class="form-label">علامات للبحث</label>
                        <input type="text" class="form-control" id="tags" name="tags" value="{{ contract.tags or '' }}">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- العمود الأيمن -->
        <div class="col-md-4">
            <!-- التواريخ والمدة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>
                        التواريخ والمدة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ contract.start_date|strftime("%Y-%m-%d") if contract.start_date else '' }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="end_date" class="form-label">تاريخ النهاية</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ contract.end_date|strftime("%Y-%m-%d") if contract.end_date else '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="duration_months" class="form-label">المدة (بالأشهر)</label>
                        <input type="number" class="form-control" id="duration_months" name="duration_months" 
                               value="{{ contract.duration_months or '' }}" min="1">
                    </div>
                </div>
            </div>
            
            <!-- المبالغ المالية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill me-2"></i>
                        المبالغ المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="total_amount" class="form-label">إجمالي المبلغ <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="total_amount" name="total_amount" 
                               value="{{ contract.total_amount }}" step="0.01" min="0" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="currency" class="form-label">العملة</label>
                        <select class="form-select" id="currency" name="currency">
                            <option value="SAR" {{ 'selected' if contract.currency == 'SAR' }}>ريال سعودي (SAR)</option>
                            <option value="USD" {{ 'selected' if contract.currency == 'USD' }}>دولار أمريكي (USD)</option>
                            <option value="EUR" {{ 'selected' if contract.currency == 'EUR' }}>يورو (EUR)</option>
                            <option value="AED" {{ 'selected' if contract.currency == 'AED' }}>درهم إماراتي (AED)</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- الحالة والأولوية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        الحالة والأولوية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">حالة العقد</label>
                        <select class="form-select" id="status" name="status">
                            <option value="draft" {{ 'selected' if contract.status == 'draft' }}>مسودة</option>
                            <option value="active" {{ 'selected' if contract.status == 'active' }}>نشط</option>
                            <option value="completed" {{ 'selected' if contract.status == 'completed' }}>مكتمل</option>
                            <option value="cancelled" {{ 'selected' if contract.status == 'cancelled' }}>ملغي</option>
                            <option value="expired" {{ 'selected' if contract.status == 'expired' }}>منتهي</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="priority" class="form-label">الأولوية</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="low" {{ 'selected' if contract.priority == 'low' }}>منخفضة</option>
                            <option value="medium" {{ 'selected' if contract.priority == 'medium' }}>متوسطة</option>
                            <option value="high" {{ 'selected' if contract.priority == 'high' }}>عالية</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- رفع ملف PDF -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf me-2"></i>
                        ملف العقد
                    </h5>
                </div>
                <div class="card-body">
                    {% if contract.pdf_file_path %}
                    <div class="mb-3">
                        <label class="form-label">الملف الحالي</label>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger me-2"></i>
                            <span>{{ contract.original_filename }}</span>
                        </div>
                        <a href="{{ url_for('contracts.download', id=contract.id) }}" class="btn btn-sm btn-outline-success mt-2">
                            <i class="fas fa-download me-1"></i>
                            تحميل
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="pdf_file" class="form-label">
                            {% if contract.pdf_file_path %}
                                استبدال الملف
                            {% else %}
                                رفع ملف PDF
                            {% endif %}
                        </label>
                        <input type="file" class="form-control" id="pdf_file" name="pdf_file" accept=".pdf">
                        <div class="form-text">يجب أن يكون الملف من نوع PDF فقط</div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{{ url_for('contracts.view', id=contract.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// حساب تاريخ النهاية تلقائياً عند تغيير المدة
document.getElementById('duration_months').addEventListener('change', function() {
    const startDate = document.getElementById('start_date').value;
    const durationMonths = parseInt(this.value);
    
    if (startDate && durationMonths) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setMonth(end.getMonth() + durationMonths);
        
        document.getElementById('end_date').value = end.toISOString().split('T')[0];
    }
});

// حساب المدة تلقائياً عند تغيير تاريخ النهاية
document.getElementById('end_date').addEventListener('change', function() {
    const startDate = document.getElementById('start_date').value;
    const endDate = this.value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        const diffTime = Math.abs(end - start);
        const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));
        
        document.getElementById('duration_months').value = diffMonths;
    }
});
</script>
{% endblock %}
