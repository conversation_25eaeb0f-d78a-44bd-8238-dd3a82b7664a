#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت الإصلاح النهائي الشامل لجميع الأخطاء
"""

import os
import re
import shutil
from datetime import datetime

def fix_all_errors():
    """إصلاح جميع الأخطاء نهائياً"""
    
    print("🔧 سكريبت الإصلاح النهائي الشامل")
    print("=" * 50)
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = "final_fix_backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    fixed_files = 0
    total_fixes = 0
    
    # قائمة الأخطاء المتكررة وإصلاحاتها
    js_fixes = [
        (r'=== = = = =', '==='),
        (r'=== = = =', '==='),
        (r'=== = =', '==='),
        (r'=== =', '==='),
        (r'!== == === === = === = =', '!=='),
        (r'!== ==', '!=='),
        (r'!== = =', '!=='),
        (r'=== =  ', '=== '),
        (r'===  ', '=== '),
    ]
    
    # البحث في جميع ملفات HTML
    for root, dirs, files in os.walk("templates"):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    file_fixes = 0
                    
                    # تطبيق جميع الإصلاحات
                    for pattern, replacement in js_fixes:
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            file_fixes += len(matches)
                    
                    if content != original_content:
                        # إنشاء نسخة احتياطية
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        backup_file = os.path.join(backup_dir, f"{file}_{timestamp}.backup")
                        shutil.copy2(file_path, backup_file)
                        
                        # حفظ الملف المُصلح
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        total_fixes += file_fixes
                        fixed_files += 1
                        
                        print(f"✅ تم إصلاح {file_path} - {file_fixes} إصلاح")
                
                except Exception as e:
                    print(f"❌ خطأ في معالجة {file_path}: {e}")
    
    # إصلاح ملف app.py
    app_fixes = [
        (r'db\.get\(', 'db.session.get('),
        (r'db\.query\(', 'db.session.query('),
        (r'request\.POST\[', 'request.form['),
        (r'request\.GET\.get\(', 'request.args.get('),
        (r'db\.save\(\)', 'db.session.commit()'),
        (r'return JsonResponse\(', 'return jsonify('),
        (r'return render\(', 'return render_template('),
    ]
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        app_file_fixes = 0
        
        for pattern, replacement in app_fixes:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                app_file_fixes += len(matches)
        
        if content != original_content:
            # إنشاء نسخة احتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"app.py_{timestamp}.backup")
            shutil.copy2('app.py', backup_file)
            
            # حفظ الملف المُصلح
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            total_fixes += app_file_fixes
            fixed_files += 1
            
            print(f"✅ تم إصلاح app.py - {app_file_fixes} إصلاح")
    
    except Exception as e:
        print(f"❌ خطأ في معالجة app.py: {e}")
    
    print(f"\n🎉 تم الانتهاء!")
    print(f"📁 الملفات المُصلحة: {fixed_files}")
    print(f"🔧 إجمالي الإصلاحات: {total_fixes}")
    print(f"💾 النسخ الاحتياطية في: {backup_dir}")

if __name__ == "__main__":
    fix_all_errors()
