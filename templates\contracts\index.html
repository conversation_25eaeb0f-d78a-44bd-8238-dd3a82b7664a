{% extends "base.html" %}

{% block title %}إدارة العقود - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-contract me-2"></i>
        إدارة العقود
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ {% url 'contracts.add' %} }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة عقد جديد
            </a>
            <a href="{{ {% url 'contracts.reports' %} }}" class="btn btn-outline-info">
                <i class="fas fa-chart-bar me-1"></i>
                التقارير
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total }}</h4>
                        <p class="card-text">إجمالي العقود</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-contract fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.active }}</h4>
                        <p class="card-text">العقود النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.draft }}</h4>
                        <p class="card-text">المسودات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.expired }}</h4>
                        <p class="card-text">منتهية الصلاحية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ {% url 'contracts.index' %} }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="search" class="form-label">البحث النصي</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="رقم العقد، العنوان، اسم العميل...">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="draft" {{ 'selected' if status_filter == 'draft' }}>مسودة</option>
                            <option value="active" {{ 'selected' if status_filter == 'active' }}>نشط</option>
                            <option value="completed" {{ 'selected' if status_filter == 'completed' }}>مكتمل</option>
                            <option value="cancelled" {{ 'selected' if status_filter == 'cancelled' }}>ملغي</option>
                            <option value="expired" {{ 'selected' if status_filter == 'expired' }}>منتهي</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="contract_type" class="form-label">نوع العقد</label>
                        <select class="form-select" id="contract_type" name="contract_type">
                            <option value="">جميع الأنواع</option>
                            {% for type in contract_types %}
                            <option value="{{ type }}" {{ 'selected' if contract_type_filter == type }}>{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="client" class="form-label">العميل</label>
                        <select class="form-select" id="client" name="client">
                            <option value="">جميع العملاء</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}" {{ 'selected' if client_filter == client.id|string }}>{{ client.full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="mb-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="mb-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول العقود -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة العقود ({{ contracts|length }} عقد)
        </h5>
    </div>
    <div class="card-body">
        {% if contracts %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم العقد</th>
                        <th>العنوان</th>
                        <th>العميل</th>
                        <th>نوع العقد</th>
                        <th>المبلغ</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>الحالة</th>
                        <th>الملف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for contract in contracts %}
                    <tr>
                        <td>
                            <strong>{{ contract.contract_number }}</strong>
                        </td>
                        <td>
                            <a href="{{ url_for('contracts.view', id=contract.id) }}" class="text-decoration-none">
                                {{ contract.title }}
                            </a>
                        </td>
                        <td>{{ contract.client_name }}</td>
                        <td>
                            <span class="badge bg-info">{{ contract.contract_type }}</span>
                        </td>
                        <td>
                            <strong>{{ "{:,.2f}"|contract.total_amount }}</strong>
                            <small class="text-muted">{{ contract.currency }}</small>
                        </td>
                        <td>{{ contract.start_date|strftime("%Y-%m-%d") if contract.start_date else '-' }}</td>
                        <td>
                            {% if contract.end_date %}
                                {{ contract.end_date|strftime("%Y-%m-%d") }}
                                {% if contract.is_expired %}
                                    <i class="fas fa-exclamation-triangle text-danger ms-1" title="منتهي الصلاحية"></i>
                                {% elif contract.days_remaining and contract.days_remaining <= 30 %}
                                    <i class="fas fa-clock text-warning ms-1" title="{{ contract.days_remaining }} يوم متبقي"></i>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if contract.status == 'draft' %}
                                <span class="badge bg-secondary">{{ contract.status_display }}</span>
                            {% elif contract.status == 'active' %}
                                <span class="badge bg-success">{{ contract.status_display }}</span>
                            {% elif contract.status == 'completed' %}
                                <span class="badge bg-primary">{{ contract.status_display }}</span>
                            {% elif contract.status == 'cancelled' %}
                                <span class="badge bg-danger">{{ contract.status_display }}</span>
                            {% elif contract.status == 'expired' %}
                                <span class="badge bg-warning">{{ contract.status_display }}</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ contract.status_display }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if contract.pdf_file_path %}
                                <a href="{{ url_for('contracts.download', id=contract.id) }}" 
                                   class="btn btn-outline-success btn-sm" title="تحميل الملف">
                                    <i class="fas fa-download"></i>
                                </a>
                            {% else %}
                                <span class="text-muted">لا يوجد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('contracts.view', id=contract.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('contracts.edit', id=contract.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger delete-contract-btn"
                                        data-contract-id="{{ contract.id }}"
                                        data-contract-number="{{ contract.contract_number }}"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عقود</h5>
            <p class="text-muted">لم يتم العثور على أي عقود تطابق معايير البحث</p>
            <a href="{{ {% url 'contracts.add' %} }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة عقد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// إضافة event listeners للأزرار
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-contract-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contractId = this.dataset.contractId;
            const contractNumber = this.dataset.contractNumber;
            deleteContract(contractId, contractNumber);
        });
    });
});

function deleteContract(contractId, contractNumber) {
    if (confirm(`هل أنت متأكد من حذف العقد ${contractNumber}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/contracts/delete/${contractId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
