try:
    from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from werkzeug.utils import secure_filename
    import os
    from datetime import datetime

    # محاولة تحميل python-dotenv
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv غير متوفر، سيتم استخدام متغيرات البيئة الافتراضية")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات المطلوبة: {e}")
    print("يرجى تشغيل: python fix_flask.py")
    print("أو: pip install Flask Flask-SQLAlchemy Flask-Login")
    exit(1)

# إنشاء تطبيق Flask
app = Flask(__name__)

# إعدادات التطبيق
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///legal_system.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# إنشاء مجلد الرفع إذا لم يكن موجوداً
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# إنشاء النماذج
from models import create_models
models_dict = create_models(db)

# استخراج النماذج
User = models_dict['User']
Role = models_dict['Role']
Permission = models_dict['Permission']
Client = models_dict['Client']
Lawyer = models_dict['Lawyer']
Case = models_dict['Case']
CaseSession = models_dict['CaseSession']
Appointment = models_dict['Appointment']
Document = models_dict['Document']
Invoice = models_dict['Invoice']
Payment = models_dict['Payment']
CaseNote = models_dict['CaseNote']
CaseTimeline = models_dict['CaseTimeline']
Contract = models_dict['Contract']

# حفظ النماذج في إعدادات التطبيق للوصول إليها من المسارات
app.config['MODELS'] = models_dict
app.config['MODELS']['db'] = db

# إضافة دوال مساعدة للقوالب
@app.template_filter('format_file_size')
@app.template_global('format_file_size')
def format_file_size(file_size):
    """تنسيق حجم الملف"""
    if not file_size or file_size == 0:
        return '0 Bytes'
    k = 1024
    sizes = ['Bytes', 'KB', 'MB', 'GB']
    i = 0
    size = float(file_size)
    while size >= k and i < len(sizes) - 1:
        size /= k
        i += 1
    return f"{size:.1f} {sizes[i]}"

# إضافة Jinja2 filters مخصصة
@app.template_filter('strftime')
def strftime_filter(date, format='%Y-%m-%d'):
    """تنسيق التاريخ"""
    if date:
        return date.strftime(format)
    return ''

@app.template_filter('truncate')
def truncate_filter(text, length=50, end='...'):
    """قطع النص"""
    if text and len(text) > length:
        return text[:length] + end
    return text or ''

@app.template_filter('round')
def round_filter(value, precision=2):
    """تقريب الأرقام"""
    try:
        return round(float(value), precision)
    except (ValueError, TypeError):
        return value

@app.template_filter('default')
def default_filter(value, default_value=''):
    """القيمة الافتراضية"""
    return value if value is not None else default_value

# دالة مساعدة لجلب إعدادات النظام
def get_system_settings():
    """جلب إعدادات النظام للقوالب"""
    try:
        SystemSettings = models_dict.get('SystemSettings')
        if SystemSettings:
            return {
                'company_name': SystemSettings.get_setting('company_name', 'مكتب الشؤون القانونية'),
                'company_subtitle': SystemSettings.get_setting('company_subtitle', 'نظام إدارة الموارد البشرية والشؤون القانونية'),
                'company_address': SystemSettings.get_setting('company_address', ''),
                'company_phone': SystemSettings.get_setting('company_phone', ''),
                'company_email': SystemSettings.get_setting('company_email', ''),
                'company_website': SystemSettings.get_setting('company_website', ''),
                'logo_path': SystemSettings.get_setting('company_logo', 'images/logo.svg'),
                'show_logo': SystemSettings.get_setting('reports_show_logo', 'true'),
                'show_company_info': SystemSettings.get_setting('reports_show_company_info', 'true'),
                'show_print_time': SystemSettings.get_setting('reports_show_print_time', 'true'),
                'show_signatures': SystemSettings.get_setting('reports_show_signatures', 'true'),
                'footer_text': SystemSettings.get_setting('reports_footer_text', 'تم إنشاء هذا التقرير تلقائياً بواسطة النظام')
            }
    except Exception as e:
        print(f"خطأ في جلب إعدادات النظام: {e}")

    # القيم الافتراضية في حالة الخطأ
    return {
        'company_name': 'مكتب الشؤون القانونية',
        'company_subtitle': 'نظام إدارة الموارد البشرية والشؤون القانونية',
        'company_address': '',
        'company_phone': '',
        'company_email': '',
        'company_website': '',
        'logo_path': 'images/logo.svg',
        'show_logo': 'true',
        'show_company_info': 'true',
        'show_print_time': 'true',
        'show_signatures': 'true',
        'footer_text': 'تم إنشاء هذا التقرير تلقائياً بواسطة النظام'
    }

# إضافة الإعدادات لجميع القوالب
@app.context_processor
def inject_system_settings():
    """حقن إعدادات النظام في جميع القوالب"""
    return get_system_settings()

@app.template_global('get_file_icon')
def get_file_icon(file_type):
    """الحصول على أيقونة الملف"""
    if not file_type:
        return 'file'
    icons = {
        'pdf': 'pdf',
        'doc': 'word',
        'docx': 'word',
        'txt': 'text',
        'jpg': 'image',
        'jpeg': 'image',
        'png': 'image',
        'gif': 'image'
    }
    return icons.get(file_type.lower(), 'file')

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# الصفحة الرئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    try:
        from datetime import date, timedelta

        # إحصائيات القضايا
        total_cases = Case.query.filter_by(is_active=True).count()
        active_cases = Case.query.filter_by(case_status='active', is_active=True).count()
        pending_cases = Case.query.filter_by(case_status='pending', is_active=True).count()
        closed_cases = Case.query.filter_by(case_status='closed', is_active=True).count()
        won_cases = Case.query.filter_by(case_status='won', is_active=True).count()

        # إحصائيات العملاء
        total_clients = Client.query.filter_by(is_active=True).count()
        individual_clients = Client.query.filter_by(client_type='individual', is_active=True).count()
        company_clients = Client.query.filter_by(client_type='company', is_active=True).count()

        # إحصائيات المحامين
        total_lawyers = Lawyer.query.filter_by(is_active=True).count()

        # إحصائيات المستخدمين
        total_users = User.query.filter_by(is_active=True).count()
        admin_users = User.query.filter_by(role='admin', is_active=True).count()

        # إحصائيات الجلسات
        today = date.today()
        upcoming_sessions = CaseSession.query.filter(
            CaseSession.session_date >= datetime.now(),
            CaseSession.session_status == 'scheduled'
        ).count()

        today_sessions = CaseSession.query.filter(
            CaseSession.session_date >= datetime.combine(today, datetime.min.time()),
            CaseSession.session_date < datetime.combine(today + timedelta(days=1), datetime.min.time()),
            CaseSession.session_status == 'scheduled'
        ).count()

        # إحصائيات المواعيد
        total_appointments = Appointment.query.count()
        upcoming_appointments = Appointment.query.filter(
            Appointment.appointment_date >= datetime.now(),
            Appointment.status == 'scheduled'
        ).count()

        today_appointments = Appointment.query.filter(
            Appointment.appointment_date >= datetime.combine(today, datetime.min.time()),
            Appointment.appointment_date < datetime.combine(today + timedelta(days=1), datetime.min.time()),
            Appointment.status == 'scheduled'
        ).count()

        # إحصائيات المستندات
        total_documents = Document.query.filter_by(is_active=True).count()

        # إحصائيات الفواتير
        try:
            total_invoices = Invoice.query.count()
            pending_invoices = Invoice.query.filter_by(invoice_status='pending').count()
            paid_invoices = Invoice.query.filter_by(invoice_status='paid').count()
            overdue_invoices = Invoice.query.filter(
                Invoice.due_date < today,
                Invoice.invoice_status.in_(['pending', 'overdue'])
            ).count()
        except Exception as e:
            print(f"خطأ في إحصائيات الفواتير: {e}")
            total_invoices = 0
            pending_invoices = 0
            paid_invoices = 0
            overdue_invoices = 0

        # حساب إجمالي المبالغ
        try:
            from sqlalchemy import func
            total_revenue = db.session.query(func.sum(Invoice.total_amount)).filter_by(invoice_status='paid').scalar() or 0
            pending_revenue = db.session.query(func.sum(Invoice.total_amount)).filter_by(invoice_status='pending').scalar() or 0
        except Exception as e:
            print(f"خطأ في حساب الإيرادات: {e}")
            total_revenue = 0
            pending_revenue = 0

        # إحصائيات هذا الشهر
        first_day_month = today.replace(day=1)
        cases_this_month = Case.query.filter(
            Case.created_at >= first_day_month,
            Case.is_active == True
        ).count()

        clients_this_month = Client.query.filter(
            Client.created_at >= first_day_month,
            Client.is_active == True
        ).count()

        # أحدث الأنشطة (آخر 5 قضايا)
        recent_cases = Case.query.filter_by(is_active=True).order_by(Case.created_at.desc()).limit(5).all()

        # أحدث العملاء (آخر 5 عملاء)
        recent_clients = Client.query.filter_by(is_active=True).order_by(Client.created_at.desc()).limit(5).all()

        return render_template('dashboard.html',
                             # إحصائيات القضايا
                             total_cases=total_cases,
                             active_cases=active_cases,
                             pending_cases=pending_cases,
                             closed_cases=closed_cases,
                             won_cases=won_cases,
                             cases_this_month=cases_this_month,

                             # إحصائيات العملاء
                             total_clients=total_clients,
                             individual_clients=individual_clients,
                             company_clients=company_clients,
                             clients_this_month=clients_this_month,

                             # إحصائيات أخرى
                             total_lawyers=total_lawyers,
                             total_users=total_users,
                             admin_users=admin_users,

                             # إحصائيات الجلسات والمواعيد
                             upcoming_sessions=upcoming_sessions,
                             today_sessions=today_sessions,
                             total_appointments=total_appointments,
                             upcoming_appointments=upcoming_appointments,
                             today_appointments=today_appointments,

                             # إحصائيات المستندات
                             total_documents=total_documents,

                             # إحصائيات الفواتير
                             total_invoices=total_invoices,
                             pending_invoices=pending_invoices,
                             paid_invoices=paid_invoices,
                             overdue_invoices=overdue_invoices,
                             total_revenue=float(total_revenue),
                             pending_revenue=float(pending_revenue),

                             # البيانات الحديثة
                             recent_cases=recent_cases,
                             recent_clients=recent_clients)

    except Exception as e:
        print(f"خطأ في لوحة التحكم: {e}")
        # في حالة الخطأ، إرجاع قيم افتراضية
        return render_template('dashboard.html',
                             total_cases=0, active_cases=0, pending_cases=0, closed_cases=0, won_cases=0,
                             total_clients=0, individual_clients=0, company_clients=0,
                             total_lawyers=0, total_users=0, admin_users=0,
                             upcoming_sessions=0, today_sessions=0,
                             total_appointments=0, upcoming_appointments=0, today_appointments=0,
                             total_documents=0, total_invoices=0, pending_invoices=0, paid_invoices=0,
                             overdue_invoices=0, total_revenue=0, pending_revenue=0,
                             cases_this_month=0, clients_this_month=0,
                             recent_cases=[], recent_clients=[])

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username, is_active=True).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('index'))

# إضافة دوال مساعدة للقوالب
@app.context_processor
def inject_settings():
    """حقن الإعدادات في جميع القوالب"""
    def get_current_logo():
        try:
            import json
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                return settings.get('current_logo', '/static/images/default-logo.svg')
        except:
            return '/static/images/default-logo.svg'

    def get_logo_settings():
        try:
            import json
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                return {
                    'height': settings.get('logo_height', 40),
                    'padding': settings.get('logo_padding', 5),
                    'border_width': settings.get('logo_border_width', 2),
                    'shadow': settings.get('logo_shadow', 8),
                    'circular': settings.get('logo_circular', True)
                }
        except:
            return {
                'height': 40,
                'padding': 5,
                'border_width': 2,
                'shadow': 8,
                'circular': True
            }

    def get_system_settings():
        try:
            import json
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
                return settings
        except:
            return {}

    def format_currency(amount, currency_code=None):
        try:
            from currency_config import format_currency as format_curr
            if not currency_code:
                # الحصول على العملة من الإعدادات
                settings = get_system_settings()
                currency_code = settings.get('currency', 'SAR')
            return format_curr(amount, currency_code)
        except:
            return f"{amount} ر.س"

    def get_currency_symbol(currency_code=None):
        try:
            from currency_config import get_currency_symbol as get_symbol
            if not currency_code:
                settings = get_system_settings()
                currency_code = settings.get('currency', 'SAR')
            return get_symbol(currency_code)
        except:
            return "ر.س"

    return dict(
        get_current_logo=get_current_logo,
        get_logo_settings=get_logo_settings,
        get_system_settings=get_system_settings,
        format_currency=format_currency,
        get_currency_symbol=get_currency_symbol
    )

# استيراد المسارات مع معالجة الأخطاء
def register_routes():
    """تسجيل جميع المسارات"""
    try:
        # استيراد مسارات القضايا
        from routes.cases import bp as cases_bp
        app.register_blueprint(cases_bp)
        print("✅ تم تحميل مسارات القضايا")
    except Exception as e:
        print(f"❌ خطأ في تحميل مسارات القضايا: {e}")

    try:
        # استيراد مسارات العملاء
        from routes.clients import bp as clients_bp
        app.register_blueprint(clients_bp)
        print("✅ تم تحميل مسارات العملاء")
    except Exception as e:
        print(f"❌ خطأ في تحميل مسارات العملاء: {e}")

    # محاولة تحميل باقي المسارات
    route_modules = ['lawyers', 'appointments', 'documents', 'invoices', 'reports', 'admin', 'contracts', 'employees', 'settings', 'attendance', 'leaves', 'warnings', 'penalties']
    for module_name in route_modules:
        try:
            module = __import__(f'routes.{module_name}', fromlist=['bp'])
            app.register_blueprint(module.bp)
            print(f"✅ تم تحميل مسارات {module_name}")
        except Exception as e:
            print(f"⚠️  لم يتم تحميل مسارات {module_name}: {e}")

# معالجات الأخطاء
@app.errorhandler(413)
def request_entity_too_large(error):
    """معالج خطأ حجم الملف كبير جداً"""
    flash('حجم الملف كبير جداً. الحد الأقصى المسموح هو 50 ميجابايت.', 'error')
    return redirect(request.referrer or url_for('dashboard'))

# مسارات API للتنبيهات
@app.route('/api/notifications/mark-read', methods=['POST'])
@login_required
def mark_notification_read():
    """وضع علامة قراءة على تنبيه"""
    try:
        data = request.get_json()
        notification_id = data.get('notification_id')

        # هنا يمكن إضافة منطق حفظ حالة التنبيه في قاعدة البيانات
        # مؤقتاً سنرجع نجاح العملية

        return jsonify({
            'success': True,
            'message': 'تم وضع علامة قراءة على التنبيه'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """وضع علامة قراءة على جميع التنبيهات"""
    try:
        # هنا يمكن إضافة منطق حفظ حالة جميع التنبيهات في قاعدة البيانات
        # مؤقتاً سنرجع نجاح العملية

        return jsonify({
            'success': True,
            'message': 'تم وضع علامة قراءة على جميع التنبيهات'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@app.route('/api/notifications/get', methods=['GET'])
@login_required
def get_notifications():
    """الحصول على التنبيهات"""
    try:
        # قائمة فارغة من التنبيهات
        notifications = []

        return jsonify({
            'success': True,
            'notifications': notifications,
            'unread_count': 0
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@app.route('/notifications')
@login_required
def notifications_page():
    """صفحة جميع التنبيهات"""
    return render_template('notifications.html')

# مسارات API لإعدادات الخادم
@app.route('/api/server/settings', methods=['GET'])
@login_required
def get_server_settings():
    """الحصول على إعدادات الخادم الحالية"""
    try:
        import json

        # قراءة إعدادات الخادم من ملف server_config.json
        try:
            with open('server_config.json', 'r', encoding='utf-8') as f:
                server_config = json.load(f)
        except FileNotFoundError:
            # إعدادات افتراضية إذا لم يوجد الملف
            server_config = {
                "host": "0.0.0.0",
                "port": 5000,
                "debug": True,
                "threaded": True,
                "use_reloader": False,
                "network_settings": {
                    "allow_external_access": True,
                    "max_connections": 100,
                    "timeout": 30
                },
                "security_settings": {
                    "enable_https": False,
                    "ssl_cert_path": "",
                    "ssl_key_path": ""
                },
                "performance_settings": {
                    "enable_caching": True,
                    "cache_timeout": 300,
                    "enable_compression": True
                }
            }

        return jsonify({
            'success': True,
            'settings': server_config
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@app.route('/api/server/settings', methods=['POST'])
@login_required
def save_server_settings():
    """حفظ إعدادات الخادم"""
    try:
        import json

        # التحقق من صلاحيات المدير
        if current_user.role != 'admin':
            return jsonify({
                'success': False,
                'message': 'غير مصرح لك بتعديل إعدادات الخادم'
            }), 403

        data = request.get_json()

        # التحقق من صحة البيانات
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم إرسال بيانات'
            }), 400

        # التحقق من صحة المنفذ
        port = data.get('port', 5000)
        if not isinstance(port, int) or port < 1 or port > 65535:
            return jsonify({
                'success': False,
                'message': 'رقم المنفذ غير صحيح (يجب أن يكون بين 1 و 65535)'
            }), 400

        # إنشاء ملف الإعدادات الجديد
        server_config = {
            "host": data.get('host', '0.0.0.0'),
            "port": port,
            "debug": data.get('debug', True),
            "threaded": data.get('threaded', True),
            "use_reloader": data.get('use_reloader', False),
            "description": "إعدادات خادم نظام الشؤون القانونية",
            "network_settings": data.get('network_settings', {
                "allow_external_access": True,
                "max_connections": 100,
                "timeout": 30
            }),
            "security_settings": data.get('security_settings', {
                "enable_https": False,
                "ssl_cert_path": "",
                "ssl_key_path": ""
            }),
            "performance_settings": data.get('performance_settings', {
                "enable_caching": True,
                "cache_timeout": 300,
                "enable_compression": True
            })
        }

        # حفظ الإعدادات في الملف
        with open('server_config.json', 'w', encoding='utf-8') as f:
            json.dump(server_config, f, ensure_ascii=False, indent=2)

        return jsonify({
            'success': True,
            'message': 'تم حفظ إعدادات الخادم بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في حفظ الإعدادات: {str(e)}'
        }), 500

@app.route('/api/server/restart', methods=['POST'])
@login_required
def restart_server():
    """إعادة تشغيل الخادم"""
    try:
        # التحقق من صلاحيات المدير
        if current_user.role != 'admin':
            return jsonify({
                'success': False,
                'message': 'غير مصرح لك بإعادة تشغيل الخادم'
            }), 403

        # إرسال إشارة إعادة التشغيل
        import threading
        import time
        import os
        import sys

        def restart_app():
            time.sleep(2)  # انتظار قصير للسماح بإرسال الاستجابة
            os.execv(sys.executable, ['python'] + sys.argv)

        # تشغيل إعادة التشغيل في خيط منفصل
        restart_thread = threading.Thread(target=restart_app)
        restart_thread.daemon = True
        restart_thread.start()

        return jsonify({
            'success': True,
            'message': 'تم إرسال أمر إعادة التشغيل'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في إعادة التشغيل: {str(e)}'
        }), 500

@app.route('/api/server/network-info', methods=['GET'])
@login_required
def get_network_info():
    """الحصول على معلومات الشبكة"""
    try:
        import socket
        import json

        # قراءة إعدادات الخادم
        try:
            with open('server_config.json', 'r', encoding='utf-8') as f:
                server_config = json.load(f)
        except FileNotFoundError:
            server_config = {
                "host": "0.0.0.0",
                "port": 5000,
                "debug": True
            }

        # الحصول على عنوان IP المحلي
        def get_local_ip():
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                ip = s.getsockname()[0]
                s.close()
                return ip
            except:
                return "127.0.0.1"

        # التحقق من توفر المنفذ
        def check_port_availability(port):
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                s.close()
                return result != 0
            except:
                return False

        local_ip = get_local_ip()
        port = server_config.get('port', 5000)
        is_https = server_config.get('security_settings', {}).get('enable_https', False)
        protocol = 'https' if is_https else 'http'

        network_info = {
            'local_ip': local_ip,
            'port': port,
            'protocol': protocol,
            'local_url': f'{protocol}://localhost:{port}',
            'network_url': f'{protocol}://{local_ip}:{port}',
            'is_https': is_https,
            'debug_mode': server_config.get('debug', False),
            'port_available': check_port_availability(port),
            'host': server_config.get('host', '0.0.0.0')
        }

        return jsonify({
            'success': True,
            'network_info': network_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

# تسجيل المسارات
register_routes()

if __name__ == '__main__':
    try:
        with app.app_context():
            db.create_all()

            # إنشاء الإعدادات الافتراضية
            try:
                SystemSettings = models_dict.get('SystemSettings')
                if SystemSettings:
                    # التحقق من وجود إعدادات افتراضية
                    if SystemSettings.query.count() == 0:
                        default_settings = [
                            ('company_name', 'مكتب الشؤون القانونية', 'text', 'company', 'اسم الشركة', 'اسم الشركة الذي يظهر في التقارير'),
                            ('company_subtitle', 'نظام إدارة الموارد البشرية والشؤون القانونية', 'text', 'company', 'وصف الشركة', 'الوصف الذي يظهر تحت اسم الشركة'),
                            ('company_logo', 'images/logo.svg', 'file', 'company', 'شعار الشركة', 'الشعار الذي يظهر في التقارير'),
                            ('reports_show_logo', 'true', 'boolean', 'reports', 'إظهار الشعار', 'إظهار شعار الشركة في التقارير'),
                            ('reports_show_company_info', 'true', 'boolean', 'reports', 'إظهار معلومات الشركة', 'إظهار اسم ووصف الشركة'),
                            ('reports_show_print_time', 'true', 'boolean', 'reports', 'إظهار وقت الطباعة', 'إظهار تاريخ ووقت الطباعة'),
                            ('reports_show_signatures', 'true', 'boolean', 'reports', 'إظهار التوقيعات', 'إظهار مربعات التوقيعات'),
                            ('reports_footer_text', 'تم إنشاء هذا التقرير تلقائياً بواسطة النظام', 'text', 'reports', 'نص الذيل', 'النص الذي يظهر في ذيل التقرير')
                        ]

                        for key, value, setting_type, category, display_name, description in default_settings:
                            setting = SystemSettings(
                                setting_key=key,
                                setting_value=value,
                                setting_type=setting_type,
                                category=category,
                                display_name=display_name,
                                description=description
                            )
                            db.add(setting)

                        db.session.commit()
                        print("✅ تم إنشاء الإعدادات الافتراضية")
            except Exception as e:
                print(f"⚠️  خطأ في إنشاء الإعدادات الافتراضية: {e}")

            # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
            if not User.query.first():
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    full_name='مدير النظام',
                    phone='123456789',
                    role='admin',
                    is_active=True
                )
                db.add(admin_user)
                db.session.commit()
                print("تم إنشاء المستخدم الافتراضي: admin / admin123")

        # إعداد الخادم المتقدم
        import socket
        import json

        # قراءة إعدادات الخادم
        server_config = {
            'host': '0.0.0.0',
            'port': 5000,
            'debug': True,
            'threaded': True,
            'use_reloader': False
        }

        # محاولة قراءة إعدادات مخصصة
        try:
            with open('server_config.json', 'r', encoding='utf-8') as f:
                custom_config = json.load(f)
                server_config.update(custom_config)
        except FileNotFoundError:
            # إنشاء ملف إعدادات افتراضي
            with open('server_config.json', 'w', encoding='utf-8') as f:
                json.dump(server_config, f, ensure_ascii=False, indent=2)

        # الحصول على عنوان IP المحلي
        def get_local_ip():
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                ip = s.getsockname()[0]
                s.close()
                return ip
            except:
                return "localhost"

        local_ip = get_local_ip()
        port = server_config['port']

        print("🚀 بدء تشغيل نظام الشؤون القانونية...")
        print("=" * 70)
        print(f"🌐 الوصول المحلي:      http://localhost:{port}")
        print(f"🌍 الوصول من الشبكة:   http://{local_ip}:{port}")
        print(f"📱 الوصول من الهاتف:   http://{local_ip}:{port}")
        print("=" * 70)
        print("👤 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("=" * 70)
        print("🔧 إعدادات الخادم:")
        print(f"   المضيف: {server_config['host']} (جميع الشبكات)")
        print(f"   المنفذ: {server_config['port']}")
        print(f"   وضع التطوير: {'مفعل' if server_config['debug'] else 'معطل'}")
        print(f"   المعالجة المتوازية: {'مفعل' if server_config['threaded'] else 'معطل'}")
        print("=" * 70)
        print("📝 لتغيير إعدادات الخادم، عدل ملف server_config.json")
        print("🔄 لإعادة تشغيل الخادم، اضغط Ctrl+C ثم شغل البرنامج مرة أخرى")
        print("🌐 يمكن الوصول للنظام من أي جهاز على نفس الشبكة")
        print("=" * 70)

        # تهيئة الصلاحيات والأدوار عند بدء التطبيق
        try:
            from permissions_manager import initialize_permissions
            with app.app_context():
                initialize_permissions(db, models_dict)
        except Exception as e:
            print(f"⚠️  تحذير: فشل في تهيئة الصلاحيات: {e}")

        app.run(
            host=server_config['host'],
            port=server_config['port'],
            debug=server_config['debug'],
            threaded=server_config.get('threaded', True),
            use_reloader=server_config.get('use_reloader', False)
        )
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
