{% extends "base.html" %}

{% block title %}تعديل جزاء{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-edit"></i>
            تعديل جزاء - {{ penalty.employee.full_name }}
          </h3>
          <div class="card-tools">
            <a href="{{ {% url 'penalties.index' %} }}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <form id="penaltyForm" enctype="multipart/form-data">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="employee_id">الموظف <span class="text-danger">*</span></label>
                  <select class="form-control" id="employee_id" name="employee_id" required>
                    <option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" 
                            {% if penalty.employee_id == employee.id %}selected{% endif %}>
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="penalty_type">نوع الجزاء <span class="text-danger">*</span></label>
                  <select class="form-control" id="penalty_type" name="penalty_type" required>
                    <option value="">اختر نوع الجزاء</option>
                    {% for type_code, type_name in penalty_types %}
                    <option value="{{ type_code }}" 
                            {% if penalty.penalty_type == type_code %}selected{% endif %}>
                      {{ type_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="category">الفئة <span class="text-danger">*</span></label>
                  <select class="form-control" id="category" name="category" required>
                    <option value="">اختر الفئة</option>
                    {% for cat_code, cat_name in categories %}
                    <option value="{{ cat_code }}" 
                            {% if penalty.category == cat_code %}selected{% endif %}>
                      {{ cat_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="severity">مستوى الخطورة</label>
                  <select class="form-control" id="severity" name="severity">
                    {% for sev_code, sev_name in severities %}
                    <option value="{{ sev_code }}" 
                            {% if penalty.severity == sev_code %}selected{% endif %}>
                      {{ sev_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="status">الحالة</label>
                  <select class="form-control" id="status" name="status">
                    {% for status_code, status_name in statuses %}
                    <option value="{{ status_code }}" 
                            {% if penalty.status == status_code %}selected{% endif %}>
                      {{ status_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="title">عنوان الجزاء <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="title" name="title" 
                         value="{{ penalty.title }}" placeholder="عنوان مختصر للجزاء..." required>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="description">وصف الجزاء <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="description" name="description" rows="4" 
                            placeholder="وصف تفصيلي للمخالفة والجزاء المفروض..." required>{{ penalty.description }}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="incident_date">تاريخ الحادثة <span class="text-danger">*</span></label>
                  <input type="date" class="form-control" id="incident_date" name="incident_date" 
                         value="{{ penalty.incident_date|strftime("%Y-%m-%d") }}" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="penalty_date">تاريخ الجزاء <span class="text-danger">*</span></label>
                  <input type="date" class="form-control" id="penalty_date" name="penalty_date" 
                         value="{{ penalty.penalty_date|strftime("%Y-%m-%d") }}" required>
                </div>
              </div>
            </div>

            <!-- تفاصيل الجزاء حسب النوع -->
            <div class="row">
              <div class="col-md-4">
                <div class="form-group" id="amount_group">
                  <label for="amount">مبلغ الجزاء (ريال)</label>
                  <input type="number" class="form-control" id="amount" name="amount" 
                         step="0.01" min="0" placeholder="0.00"
                         value="{% if penalty.amount %}{{ '%.2f'|format(penalty.amount) }}{% endif %}">
                  <small class="form-text text-muted">للجزاءات المالية فقط</small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group" id="days_group">
                  <label for="days_count">عدد أيام الإيقاف</label>
                  <input type="number" class="form-control" id="days_count" name="days_count" 
                         min="1" placeholder="1" value="{{ penalty.days_count or '' }}">
                  <small class="form-text text-muted">لجزاءات الإيقاف عن العمل</small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group" id="payment_status_group">
                  <label for="payment_status">حالة الدفع</label>
                  <select class="form-control" id="payment_status" name="payment_status">
                    {% for pay_status_code, pay_status_name in payment_statuses %}
                    <option value="{{ pay_status_code }}" 
                            {% if penalty.payment_status == pay_status_code %}selected{% endif %}>
                      {{ pay_status_name }}
                    </option>
                    {% endfor %}
                  </select>
                  <small class="form-text text-muted">للجزاءات المالية فقط</small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="notes">ملاحظات إضافية</label>
                  <textarea class="form-control" id="notes" name="notes" rows="3" 
                            placeholder="أي ملاحظات إضافية حول الجزاء...">{{ penalty.notes or '' }}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="attachment">مرفق جديد (اختياري)</label>
                  <input type="file" class="form-control-file" id="attachment" name="attachment" 
                         accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                  <small class="form-text text-muted">
                    الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG (حد أقصى 5 ميجابايت)
                  </small>
                  {% if penalty.attachment_path %}
                  <div class="mt-2">
                    <small class="text-info">
                      <i class="fas fa-paperclip"></i>
                      يوجد مرفق حالي - سيتم استبداله عند رفع ملف جديد
                    </small>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- معلومات الجزاء -->
            <div class="row">
              <div class="col-12">
                <div class="card card-outline card-info">
                  <div class="card-header">
                    <h3 class="card-title">معلومات الجزاء</h3>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4">
                        <strong>تاريخ الإنشاء:</strong><br>
                        <small class="text-muted">{{ penalty.created_at|strftime("%Y-%m-%d %H:%M") }}</small>
                      </div>
                      <div class="col-md-4">
                        <strong>منشئ الجزاء:</strong><br>
                        <small class="text-muted">
                          {% if penalty.creator %}{{ penalty.creator.username }}{% else %}النظام{% endif %}
                        </small>
                      </div>
                      <div class="col-md-4">
                        <strong>آخر تحديث:</strong><br>
                        <small class="text-muted">{{ penalty.updated_at|strftime("%Y-%m-%d %H:%M") }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i>
              حفظ التعديلات
            </button>
            <a href="{{ {% url 'penalties.index' %} }}" class="btn btn-secondary">
              <i class="fas fa-times"></i>
              إلغاء
            </a>
            {% if penalty.penalty_type == 'financial' and penalty.payment_status == 'pending' %}
            <button type="button" class="btn btn-success" onclick="markPaid('{{ penalty.id }}')">
              <i class="fas fa-dollar-sign"></i>
              تسديد الجزاء
            </button>
            {% endif %}
            <button type="button" class="btn btn-danger float-right"
                    onclick="deletePenalty('{{ penalty.id }}')">
              <i class="fas fa-trash"></i>
              حذف الجزاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
// إظهار/إخفاء الحقول حسب نوع الجزاء
function toggleFieldsByType() {
  const penaltyType = document.getElementById('penalty_type').value;
  const amountGroup = document.getElementById('amount_group');
  const daysGroup = document.getElementById('days_group');
  const paymentStatusGroup = document.getElementById('payment_status_group');
  const amountInput = document.getElementById('amount');
  const daysInput = document.getElementById('days_count');
  
  // إعادة تعيين الحقول
  amountInput.required = false;
  daysInput.required = false;
  
  // إظهار/إخفاء الحقول المناسبة
  if (penaltyType ===  'financial') {
    amountGroup.style.display = 'block';
    paymentStatusGroup.style.display = 'block';
    daysGroup.style.display = 'none';
    amountInput.required = true;
  } else if (penaltyType ===  'suspension') {
    amountGroup.style.display = 'none';
    paymentStatusGroup.style.display = 'none';
    daysGroup.style.display = 'block';
    daysInput.required = true;
  } else {
    amountGroup.style.display = 'none';
    paymentStatusGroup.style.display = 'none';
    daysGroup.style.display = 'none';
  }
}

// تطبيق التغييرات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  toggleFieldsByType();
});

// تطبيق التغييرات عند تغيير نوع الجزاء
document.getElementById('penalty_type').addEventListener('change', toggleFieldsByType);

// التحقق من حجم الملف
document.getElementById('attachment').addEventListener('change', function() {
  const file = this.files[0];
  if (file) {
    const maxSize = 5 * 1024 * 1024; // 5 ميجابايت
    if (file.size > maxSize) {
      alert('حجم الملف يجب أن يكون أقل من 5 ميجابايت');
      this.value = '';
    }
  }
});

// إرسال النموذج
document.getElementById('penaltyForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  // التحقق من صحة التواريخ
  const incidentDate = new Date(document.getElementById('incident_date').value);
  const penaltyDate = new Date(document.getElementById('penalty_date').value);
  
  if (penaltyDate < incidentDate) {
    alert('تاريخ الجزاء يجب أن يكون بعد أو في نفس تاريخ الحادثة');
    return;
  }
  
  // التحقق من البيانات المطلوبة حسب نوع الجزاء
  const penaltyType = document.getElementById('penalty_type').value;
  const amount = document.getElementById('amount').value;
  const daysCount = document.getElementById('days_count').value;
  
  if (penaltyType ===  'financial' && (!amount || parseFloat(amount) <= 0)) {
    alert('مبلغ الجزاء مطلوب للجزاءات المالية');
    return;
  }
  
  if (penaltyType ===  'suspension' && (!daysCount || parseInt(daysCount) <= 0)) {
    alert('عدد أيام الإيقاف مطلوب');
    return;
  }
  
  const formData = new FormData(this);
  
  // إظهار مؤشر التحميل
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
  submitBtn.disabled = true;
  
  fetch('{{ url_for("penalties.edit", penalty_id=penalty.id) }}', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // إظهار رسالة نجاح
      showAlert('success', data.message);
      
      // إعادة توجيه بعد ثانيتين
      setTimeout(() => {
        window.location.href = '{% url "penalties:index" %}';
      }, 2000);
    } else {
      showAlert('danger', data.message);
      
      // إعادة تفعيل الزر
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  })
  .catch(error => {
    showAlert('danger', 'حدث خطأ في الاتصال');
    
    // إعادة تفعيل الزر
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

// تسديد جزاء مالي
function markPaid(id) {
  if (confirm('هل أنت متأكد من تسديد هذا الجزاء المالي؟')) {
    fetch(`/penalties/${id}/mark-paid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        setTimeout(() => {
          location.reload();
        }, 2000);
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'حدث خطأ في الاتصال');
    });
  }
}

// حذف الجزاء
function deletePenalty(id) {
  if (confirm('هل أنت متأكد من حذف الجزاء؟\nلا يمكن التراجع عن هذا الإجراء.')) {
    fetch(`/penalties/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        setTimeout(() => {
          window.location.href = '{% url "penalties:index" %}';
        }, 2000);
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'حدث خطأ في الاتصال');
    });
  }
}

function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;
  
  // إدراج التنبيه في أعلى الصفحة
  const container = document.querySelector('.container-fluid');
  container.insertBefore(alertDiv, container.firstChild);
  
  // إزالة التنبيه بعد 5 ثوان
  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
}

// تحديث الحد الأدنى لتاريخ الجزاء عند تغيير تاريخ الحادثة
document.getElementById('incident_date').addEventListener('change', function() {
  document.getElementById('penalty_date').min = this.value;
});
</script>
{% endblock %}
