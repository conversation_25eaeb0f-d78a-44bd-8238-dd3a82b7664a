#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت معالجة الأخطاء التلقائي
Auto Error Fixer Script

يقوم هذا السكريبت بفحص ومعالجة الأخطاء تلقائياً في:
- القوالب (Templates)
- النماذج (Models) 
- التقارير (Reports)
- ملفات JavaScript
- ملفات CSS
- ملفات Python
"""

import os
import re
import json
import logging
import sqlite3
from pathlib import Path
from typing import List, Dict, Tuple
import subprocess
import sys
from datetime import datetime

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_error_fixer.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class AutoErrorFixer:
    """فئة معالجة الأخطاء التلقائية"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.errors_fixed = 0
        self.errors_found = 0
        self.backup_dir = self.project_root / "error_fix_backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # قوائم الأخطاء الشائعة وحلولها
        self.template_fixes = {
            # Flask إلى Django
            r'url_for\([\'"]([^\'\"]+)[\'\"]\)': r"{% url '\1' %}",
            r'\{\{\s*url_for\([\'"]([^\'\"]+)[\'\"]\)\s*\}\}': r"{% url '\1' %}",
            
            # Python format إلى Django filters
            r'\{\{\s*[\'"]([^\'\"]*)\[\'"]\.format\(([^}]+)\)\s*\}\}': r'{{ \2|\1 }}',
            r'\.format\(([^}]+)\)': r'|\1',
            
            # Python methods إلى Django filters
            r'\.strftime\([\'"]([^\'\"]+)[\'"]\)': r'|date:"\1"',
            r'\[:(\d+)\]': r'|truncatechars:\1',
            r'\.items\(\)': '',
            
            # Template tags fixes
            r'\{\%\s*break\s*\%\}': '',
            r'\.round\((\d+)\)': r'|floatformat:\1',
            
            # CSS في style attributes
            r'style="[^"]*\{\%[^"]*\%\}[^"]*"': 'data-dynamic-style="true"',
        }
        
        self.python_fixes = {
            # استيراد مكتبات
            r'from django.shortcuts import': 'from django.shortcuts import',
            r'from django.shortcuts import render': 'from django.shortcuts import render',
            
            # متغيرات Flask إلى Django
            r'request\.form': 'request.POST',
            r'request\.args': 'request.GET',
            r'session\[': 'request.request.request.request.session[',
            
            # استجابات
            r'return render': 'return render',
            r'return JsonResponse': 'return JsonResponse',
            
            # قاعدة البيانات
            r'db\.session\.': 'db.',
            r'\.commit\(\)': '.save()',
            r'\.rollback\(\)': '.delete()',
        }
        
        self.js_fixes = {
            # أخطاء JavaScript شائعة
            r'console\.log\([^)]*\);?\s*$': '',  # إزالة console.log في الإنتاج
            r'debugger;?\s*$': '',  # إزالة debugger
            r'var\s+': 'let ',  # استخدام let بدلاً من var
            r'==\s*': '=== ',  # استخدام === بدلاً من ==
            r'!=\s*': '!== ',  # استخدام !== بدلاً من !=
        }

    def create_backup(self, file_path: Path) -> Path:
        """إنشاء نسخة احتياطية من الملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}_{timestamp}.backup"
        backup_path = self.backup_dir / backup_name
        
        try:
            backup_path.write_text(file_path.read_text(encoding='utf-8'), encoding='utf-8')
            logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"فشل في إنشاء نسخة احتياطية لـ {file_path}: {e}")
            return None

    def fix_template_file(self, file_path: Path) -> bool:
        """إصلاح ملف قالب"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            # تطبيق الإصلاحات
            for pattern, replacement in self.template_fixes.items():
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    self.errors_found += len(matches)
                    logger.info(f"تم إصلاح {len(matches)} خطأ في {file_path.name}: {pattern}")
            
            # إصلاحات خاصة للـ CSS في style attributes
            content = self.fix_css_in_templates(content)
            
            # حفظ التغييرات إذا كانت هناك تعديلات
            if content != original_content:
                self.create_backup(file_path)
                file_path.write_text(content, encoding='utf-8')
                self.errors_fixed += 1
                logger.info(f"تم إصلاح ملف القالب: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في إصلاح ملف القالب {file_path}: {e}")
            
        return False

    def fix_css_in_templates(self, content: str) -> str:
        """إصلاح مشاكل CSS في القوالب"""
        # البحث عن style attributes تحتوي على Django template syntax
        css_pattern = r'style="([^"]*\{\%[^"]*\%\}[^"]*)"'
        matches = re.finditer(css_pattern, content)
        
        for match in matches:
            full_match = match.group(0)
            style_content = match.group(1)
            
            # استخراج القيم من template syntax
            if 'widthratio' in style_content:
                # تحويل widthratio إلى data attributes
                data_attr_replacement = 'data-width-ratio="true" style="width: 0%"'
                content = content.replace(full_match, data_attr_replacement)
                
            elif '{{' in style_content and '}}' in style_content:
                # تحويل متغيرات Django إلى data attributes
                var_pattern = r'\{\{\s*([^}]+)\s*\}\}'
                variables = re.findall(var_pattern, style_content)
                
                data_attrs = []
                clean_style = style_content
                
                for var in variables:
                    attr_name = var.replace('.', '-').replace('|', '-').replace(':', '-')
                    data_attrs.append(f'data-{attr_name}="{{{{{var}}}}}"')
                    clean_style = re.sub(r'\{\{\s*' + re.escape(var) + r'\s*\}\}', '0', clean_style)
                
                replacement = f'{" ".join(data_attrs)} style="{clean_style}"'
                content = content.replace(full_match, replacement)
        
        return content

    def fix_python_file(self, file_path: Path) -> bool:
        """إصلاح ملف Python"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            # تطبيق الإصلاحات
            for pattern, replacement in self.python_fixes.items():
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    self.errors_found += len(matches)
                    logger.info(f"تم إصلاح {len(matches)} خطأ في {file_path.name}: {pattern}")
            
            # حفظ التغييرات إذا كانت هناك تعديلات
            if content != original_content:
                self.create_backup(file_path)
                file_path.write_text(content, encoding='utf-8')
                self.errors_fixed += 1
                logger.info(f"تم إصلاح ملف Python: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في إصلاح ملف Python {file_path}: {e}")
            
        return False

    def fix_javascript_file(self, file_path: Path) -> bool:
        """إصلاح ملف JavaScript"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            # تطبيق الإصلاحات
            for pattern, replacement in self.js_fixes.items():
                matches = re.findall(pattern, content, re.MULTILINE)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
                    self.errors_found += len(matches)
                    logger.info(f"تم إصلاح {len(matches)} خطأ في {file_path.name}: {pattern}")
            
            # حفظ التغييرات إذا كانت هناك تعديلات
            if content != original_content:
                self.create_backup(file_path)
                file_path.write_text(content, encoding='utf-8')
                self.errors_fixed += 1
                logger.info(f"تم إصلاح ملف JavaScript: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في إصلاح ملف JavaScript {file_path}: {e}")

        return False

    def fix_database_issues(self) -> bool:
        """إصلاح مشاكل قاعدة البيانات"""
        try:
            db_path = self.project_root / "instance" / "legal_system.db"
            if not db_path.exists():
                db_path = self.project_root / "legal_system.db"

            if not db_path.exists():
                logger.warning("لم يتم العثور على قاعدة البيانات")
                return False

            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # فحص الجداول المطلوبة
            required_tables = [
                'users', 'employees', 'clients', 'cases', 'appointments',
                'documents', 'invoices', 'contracts', 'penalties', 'warnings'
            ]

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]

            missing_tables = [table for table in required_tables if table not in existing_tables]

            if missing_tables:
                logger.info(f"جداول مفقودة: {missing_tables}")
                # يمكن إضافة كود لإنشاء الجداول المفقودة هنا

            # فحص وإصلاح الفهارس
            self.fix_database_indexes(cursor)

            conn.save()
            conn.close()

            logger.info("تم فحص قاعدة البيانات بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في فحص قاعدة البيانات: {e}")
            return False

    def fix_database_indexes(self, cursor):
        """إصلاح فهارس قاعدة البيانات"""
        indexes_to_create = [
            "CREATE INDEX IF NOT EXISTS idx_cases_client_id ON cases(client_id)",
            "CREATE INDEX IF NOT EXISTS idx_appointments_case_id ON appointments(case_id)",
            "CREATE INDEX IF NOT EXISTS idx_documents_case_id ON documents(case_id)",
            "CREATE INDEX IF NOT EXISTS idx_penalties_employee_id ON penalties(employee_id)",
            "CREATE INDEX IF NOT EXISTS idx_warnings_employee_id ON warnings(employee_id)",
        ]

        for index_sql in indexes_to_create:
            try:
                cursor.execute(index_sql)
                logger.info(f"تم إنشاء فهرس: {index_sql.split()[-1]}")
            except Exception as e:
                logger.warning(f"فشل في إنشاء فهرس: {e}")

    def add_javascript_for_dynamic_styles(self, template_path: Path) -> bool:
        """إضافة JavaScript لمعالجة الأنماط الديناميكية"""
        try:
            content = template_path.read_text(encoding='utf-8')

            # التحقق من وجود data-width-ratio
            if 'data-width-ratio="true"' in content and 'setDynamicStyles' not in content:
                js_code = '''
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة الأنماط الديناميكية
    function setDynamicStyles() {
        // معالجة progress bars
        const progressBars = document.querySelectorAll('[data-width-ratio="true"]');
        progressBars.forEach(bar => {
            const count = parseInt(bar.getAttribute('data-count')) || 0;
            const total = parseInt(bar.getAttribute('data-total')) || 1;
            const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
            bar.style.width = percentage + '%';
        });

        // معالجة العناصر الأخرى ذات الأنماط الديناميكية
        const dynamicElements = document.querySelectorAll('[data-dynamic-style="true"]');
        dynamicElements.forEach(element => {
            // تطبيق الأنماط حسب data attributes
            for (let attr of element.attributes) {
                if (attr.name.startsWith('data-') && attr.name !== 'data-dynamic-style') {
                    const styleProp = attr.name.replace('data-', '').replace(/-/g, '');
                    if (styleProp === 'width' || styleProp === 'height') {
                        element.style[styleProp] = attr.value;
                    }
                }
            }
        });
    }

    setDynamicStyles();
});
</script>'''

                # إضافة الكود قبل </body> أو في نهاية الملف
                if '</body>' in content:
                    content = content.replace('</body>', js_code + '\n</body>')
                else:
                    content += js_code

                template_path.write_text(content, encoding='utf-8')
                logger.info(f"تم إضافة JavaScript للأنماط الديناميكية في {template_path.name}")
                return True

        except Exception as e:
            logger.error(f"خطأ في إضافة JavaScript لـ {template_path}: {e}")

        return False

    def scan_and_fix_all(self) -> Dict[str, int]:
        """فحص وإصلاح جميع الملفات"""
        results = {
            'templates_fixed': 0,
            'python_files_fixed': 0,
            'js_files_fixed': 0,
            'total_errors_found': 0,
            'total_errors_fixed': 0
        }

        logger.info("بدء فحص وإصلاح جميع الملفات...")

        # فحص ملفات القوالب
        templates_dir = self.project_root / "templates"
        if templates_dir.exists():
            for template_file in templates_dir.rglob("*.html"):
                if self.fix_template_file(template_file):
                    results['templates_fixed'] += 1
                    # إضافة JavaScript للأنماط الديناميكية إذا لزم الأمر
                    self.add_javascript_for_dynamic_styles(template_file)

        # فحص ملفات Python
        for py_file in self.project_root.rglob("*.py"):
            if "venv" not in str(py_file) and "__pycache__" not in str(py_file):
                if self.fix_python_file(py_file):
                    results['python_files_fixed'] += 1

        # فحص ملفات JavaScript
        static_dir = self.project_root / "static"
        if static_dir.exists():
            for js_file in static_dir.rglob("*.js"):
                if self.fix_javascript_file(js_file):
                    results['js_files_fixed'] += 1

        # فحص ملفات JavaScript في القوالب
        if templates_dir.exists():
            for template_file in templates_dir.rglob("*.html"):
                content = template_file.read_text(encoding='utf-8')
                if '<script>' in content:
                    # استخراج ومعالجة JavaScript المضمن
                    self.fix_inline_javascript(template_file)

        # إصلاح قاعدة البيانات
        self.fix_database_issues()

        results['total_errors_found'] = self.errors_found
        results['total_errors_fixed'] = self.errors_fixed

        return results

    def fix_inline_javascript(self, template_path: Path) -> bool:
        """إصلاح JavaScript المضمن في القوالب"""
        try:
            content = template_path.read_text(encoding='utf-8')
            original_content = content

            # البحث عن كتل JavaScript
            js_pattern = r'<script[^>]*>(.*?)</script>'
            matches = re.finditer(js_pattern, content, re.DOTALL)

            for match in matches:
                js_content = match.group(1)
                fixed_js = js_content

                # تطبيق إصلاحات JavaScript
                for pattern, replacement in self.js_fixes.items():
                    fixed_js = re.sub(pattern, replacement, fixed_js, flags=re.MULTILINE)

                if fixed_js != js_content:
                    content = content.replace(match.group(0),
                                            match.group(0).replace(js_content, fixed_js))

            if content != original_content:
                self.create_backup(template_path)
                template_path.write_text(content, encoding='utf-8')
                logger.info(f"تم إصلاح JavaScript المضمن في {template_path.name}")
                return True

        except Exception as e:
            logger.error(f"خطأ في إصلاح JavaScript المضمن في {template_path}: {e}")

        return False

    def generate_report(self, results: Dict[str, int]) -> str:
        """إنشاء تقرير بالنتائج"""
        report = f"""
=== تقرير معالجة الأخطاء التلقائية ===
التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

النتائج:
- ملفات القوالب المُصلحة: {results['templates_fixed']}
- ملفات Python المُصلحة: {results['python_files_fixed']}
- ملفات JavaScript المُصلحة: {results['js_files_fixed']}
- إجمالي الأخطاء المكتشفة: {results['total_errors_found']}
- إجمالي الأخطاء المُصلحة: {results['total_errors_fixed']}

النسخ الاحتياطية محفوظة في: {self.backup_dir}

=== انتهى التقرير ===
        """

        # حفظ التقرير في ملف
        report_file = self.project_root / f"error_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.write_text(report, encoding='utf-8')

        return report

    def run_syntax_check(self) -> bool:
        """فحص صحة الصيغة للملفات"""
        try:
            # فحص ملفات Python
            for py_file in self.project_root.rglob("*.py"):
                if "venv" not in str(py_file) and "__pycache__" not in str(py_file):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            compile(f.read(), py_file, 'exec')
                    except SyntaxError as e:
                        logger.error(f"خطأ صيغة في {py_file}: {e}")
                        return False

            logger.info("فحص صحة الصيغة مكتمل بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في فحص الصيغة: {e}")
            return False

def main():
    """الوظيفة الرئيسية"""
    print("🔧 سكريبت معالجة الأخطاء التلقائي")
    print("=" * 50)

    # إنشاء مثيل من معالج الأخطاء
    fixer = AutoErrorFixer()

    try:
        # تشغيل فحص وإصلاح شامل
        print("📋 بدء فحص وإصلاح الأخطاء...")
        results = fixer.scan_and_fix_all()

        # إنشاء وعرض التقرير
        report = fixer.generate_report(results)
        print(report)

        # فحص صحة الصيغة بعد الإصلاح
        print("🔍 فحص صحة الصيغة...")
        if fixer.run_syntax_check():
            print("✅ جميع الملفات صحيحة صيغياً")
        else:
            print("❌ توجد أخطاء صيغة تحتاج إلى مراجعة يدوية")

        # رسالة النجاح
        if results['total_errors_fixed'] > 0:
            print(f"\n🎉 تم إصلاح {results['total_errors_fixed']} خطأ بنجاح!")
            print(f"📁 النسخ الاحتياطية محفوظة في: {fixer.backup_dir}")
        else:
            print("\n✨ لم يتم العثور على أخطاء تحتاج إلى إصلاح")

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ عام في تشغيل السكريبت: {e}")
        print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
