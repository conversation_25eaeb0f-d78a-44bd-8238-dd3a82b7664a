{% extends "base.html" %}

{% block title %}تعديل القضية - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        تعديل القضية: {{ case.case_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('cases.view', id=case.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-eye me-1"></i>
                عرض القضية
            </a>
            <a href="{{ {% url 'cases.index' %} }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات القضية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="case_number" class="form-label">رقم القضية <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="case_number" name="case_number" 
                                   value="{{ case.case_number }}" readonly>
                            <div class="form-text">رقم القضية لا يمكن تعديله</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="case_title" class="form-label">عنوان القضية <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="case_title" name="case_title" 
                                   value="{{ case.case_title }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="case_type" class="form-label">نوع القضية <span class="text-danger">*</span></label>
                            <select class="form-select" id="case_type" name="case_type" required>
                                <option value="">اختر نوع القضية</option>
                                <option value="مدني" {{ 'selected' if case.case_type == 'مدني' else '' }}>مدني</option>
                                <option value="تجاري" {{ 'selected' if case.case_type == 'تجاري' else '' }}>تجاري</option>
                                <option value="جنائي" {{ 'selected' if case.case_type == 'جنائي' else '' }}>جنائي</option>
                                <option value="عمالي" {{ 'selected' if case.case_type == 'عمالي' else '' }}>عمالي</option>
                                <option value="أسري" {{ 'selected' if case.case_type == 'أسري' else '' }}>أسري</option>
                                <option value="إداري" {{ 'selected' if case.case_type == 'إداري' else '' }}>إداري</option>
                                <option value="عقاري" {{ 'selected' if case.case_type == 'عقاري' else '' }}>عقاري</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="case_status" class="form-label">حالة القضية <span class="text-danger">*</span></label>
                            <select class="form-select" id="case_status" name="case_status" required>
                                <option value="">اختر حالة القضية</option>
                                <option value="pending" {{ 'selected' if case.case_status == 'pending' else '' }}>معلقة</option>
                                <option value="active" {{ 'selected' if case.case_status == 'active' else '' }}>نشطة</option>
                                <option value="completed" {{ 'selected' if case.case_status == 'completed' else '' }}>مكتملة</option>
                                <option value="won" {{ 'selected' if case.case_status == 'won' else '' }}>مكسوبة</option>
                                <option value="lost" {{ 'selected' if case.case_status == 'lost' else '' }}>مخسورة</option>
                                <option value="cancelled" {{ 'selected' if case.case_status == 'cancelled' else '' }}>ملغية</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="client_id" class="form-label">العميل <span class="text-danger">*</span></label>
                            <select class="form-select" id="client_id" name="client_id" required>
                                <option value="">اختر العميل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}" {{ 'selected' if client.id == case.client_id else '' }}>
                                    {{ client.full_name }} ({{ client.client_code }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lawyer_id" class="form-label">المحامي المسؤول <span class="text-danger">*</span></label>
                            <select class="form-select" id="lawyer_id" name="lawyer_id" required>
                                <option value="">اختر المحامي</option>
                                {% for lawyer in lawyers %}
                                <option value="{{ lawyer.id }}" {{ 'selected' if lawyer.id == case.lawyer_id else '' }}>
                                    {{ lawyer.user.full_name }} - {{ lawyer.specialization }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="court_name" class="form-label">اسم المحكمة</label>
                            <input type="text" class="form-control" id="court_name" name="court_name" 
                                   value="{{ case.court_name or '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="case_value" class="form-label">قيمة القضية</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="case_value" name="case_value" 
                                       value="{{ case.case_value or '' }}" step="0.01" min="0">
                                <span class="input-group-text">{{ get_currency_symbol() }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="filing_date" class="form-label">تاريخ رفع القضية</label>
                            <input type="date" class="form-control" id="filing_date" name="filing_date" 
                                   value="{{ case.filing_date|date:"%Y-%m-%d" if case.filing_date else '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="next_hearing_date" class="form-label">تاريخ الجلسة القادمة</label>
                            <input type="date" class="form-control" id="next_hearing_date" name="next_hearing_date">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="case_description" class="form-label">وصف القضية</label>
                        <textarea class="form-control" id="case_description" name="case_description" rows="4">{{ case.case_description or '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ case.notes or '' }}</textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cases.view', id=case.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">معلومات القضية</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">رقم القضية:</small>
                <p class="mb-2"><strong>{{ case.case_number }}</strong></p>
                
                <small class="text-muted">تاريخ الإنشاء:</small>
                <p class="mb-2">{{ case.filing_date|date:"%Y-%m-%d" if case.filing_date else 'غير محدد' }}</p>
                
                <small class="text-muted">آخر تحديث:</small>
                <p class="mb-0">الآن</p>
            </div>
        </div>

        <!-- إرشادات -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    إرشادات التعديل
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من صحة جميع البيانات قبل الحفظ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        رقم القضية لا يمكن تعديله
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اختر المحامي المناسب للقضية
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        أضف وصفاً مفصلاً للقضية
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const caseTitle = document.getElementById('case_title').value.trim();
    const caseType = document.getElementById('case_type').value;
    const caseStatus = document.getElementById('case_status').value;
    const clientId = document.getElementById('client_id').value;
    const lawyerId = document.getElementById('lawyer_id').value;
    
    if (!caseTitle || !caseType || !caseStatus || !clientId || !lawyerId) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
});

// تحديث قائمة المحامين حسب نوع القضية (اختياري)
document.getElementById('case_type').addEventListener('change', function() {
    // يمكن إضافة منطق لفلترة المحامين حسب التخصص
    
});
</script>
{% endblock %}
